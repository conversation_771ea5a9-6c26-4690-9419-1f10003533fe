import java.util.*;
import java.time.format.DateTimeFormatter;
import java.time.*;
import java.util.Scanner;
import java.util.ArrayList;
import java.util.List;

public class Bankingapp {
    private static ArrayList<Account> accounts = new ArrayList<>();
    private static int totalAccountsCreated = 0;
    
    public static void main(String[] args) {
        System.out.println("====Nikhil Chase & co=====");
        
        boolean running = true;
        while (running) {
            Userinputs.displayMainMenu();
            int choice = Userinputs.getMenuChoice();
            
            switch (choice) {
                case 1:
                    createAccount();
                    break;
                case 2:
                    loginToAccount();
                    break;
                case 3:
                    showTotalAccounts();
                    break;
                case 4:
                    System.out.println("Thank you for using Nikhil Chase & co! Goodbye!");
                    running = false;
                    break;
            }
        }
        
        Userinputs.closeScanner();
    }
    
    private static void createAccount() {
        System.out.println("\n===== CREATE NEW ACCOUNT =====");
        String name = Userinputs.getStringInput("Enter your name: ");
        
        
        
    }
    
    private static void loginToAccount() {
        System.out.println("\n===== LOGIN TO ACCOUNT =====");
        String accountNumber = Userinputs.getStringInput("Enter account number: ");
        String password = Userinputs.getStringInput("Enter password: ");
        
        // Search for account
        Account foundAccount = null;
        for (Account account : accounts) {
            if (account.getAccountNumber().equals(accountNumber)) {
                foundAccount = account;
                break;
            }
        }
        
        if (foundAccount != null && foundAccount.verifyPassword(password)) {
            System.out.println("Login successful! Welcome " + foundAccount.getName());
            // Add account management menu here
        } else {
            System.out.println("Invalid account number or password!");
        }
    }
    
    private static void showTotalAccounts() {
        System.out.println("\n===== ACCOUNT STATISTICS =====");
        System.out.println("Total accounts created: " + totalAccountsCreated);
        System.out.println("Currently active accounts: " + accounts.size());
    }
}