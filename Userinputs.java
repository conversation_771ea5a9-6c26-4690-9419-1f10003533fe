import java.util.Scanner;

public class Userinputs {
    private static Scanner scanner = new Scanner(System.in);
    
    public static void displayMainMenu() {
        System.out.println("\n===== MAIN MENU =====");
        System.out.println("1. Create Account");
        System.out.println("2. Login to Account");
        System.out.println("3. Show Total Accounts Created");
        System.out.println("4. Exit");
        System.out.println("=====================");
        System.out.print("Enter your choice (1-4): ");
    }
    
    public static int getMenuChoice() {
        while (true) {
            try {
                int choice = Integer.parseInt(scanner.nextLine().trim());
                if (choice >= 1 && choice <= 4) {
                    return choice;
                } else {
                    System.out.print("Invalid choice! Please enter 1-4: ");
                }
            } catch (NumberFormatException e) {
                System.out.print("Invalid input! Please enter a number (1-4): ");
            }
        }
    }
    
    public static String getStringInput(String prompt) {
        System.out.print(prompt);
        return scanner.nextLine().trim();
    }
    
    public static double getDoubleInput(String prompt) {
        while (true) {
            try {
                System.out.print(prompt);
                return Double.parseDouble(scanner.nextLine().trim());
            } catch (NumberFormatException e) {
                System.out.println("Invalid input! Please enter a valid number.");
            }
        }
    }
    
    public static int getIntInput(String prompt) {
        while (true) {
            try {
                System.out.print(prompt);
                return Integer.parseInt(scanner.nextLine().trim());
            } catch (NumberFormatException e) {
                System.out.println("Invalid input! Please enter a valid number.");
            }
        }
    }
    
    public static void closeScanner() {
        scanner.close();
    }
}