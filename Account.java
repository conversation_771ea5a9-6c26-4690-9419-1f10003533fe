import java.util.ArrayList;
import java.util.List;

public abstract class Account {
    // encapsulation
    protected String name;
    protected String accnumber;
    protected double balance;
    protected String password;
    protected String pin;
    protected String accountType;
    protected String guardian;
    protected String guardianRelation;
    protected static int accountcount = 0;
    protected String addresss;
    protected int Pincode;
    protected ArrayList<String> Transactions = new ArrayList<String>();
    
    public Account(String name, String accountNumber, double initialBalance) {
        this.name = name;
        this.accnumber = accountNumber;
        this.balance = initialBalance;
    }
    
    public abstract void deposit(double amount);
    public abstract void withdraw(double amount);
    
    public void checkBalance() {
        System.out.println("Account Holder: " + name);
        System.out.println("Account Number: " + accnumber);
        System.out.println("Current Balance: " + String.format("%.2f", balance));
    }
    
    public String getName() {
        return name;
    }
    
    public String getAccountNumber() {
        return accnumber;
    }
    
    public double getBalance() {
        return balance;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public void setPin(String pin) {
        this.pin = pin;
    }
    
    public boolean verifyPassword(String inputPassword) {
        return password != null && password.equals(inputPassword);
    }
    
    public boolean verifyPin(String inputPin) {
        return pin != null && pin.equals(inputPin);
    }
    
    public void resetPassword(String newPassword) {
        this.password = newPassword;
    }
}