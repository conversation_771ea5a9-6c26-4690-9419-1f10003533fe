// Node class to represent individual elements in the linked list
class Node {
    int data;
    Node next;

    // Constructor to create a new node
    public Node(int data) {
        this.data = data;
        this.next = null;
    }
}

public class Link {
    private Node head; // Head of the linked list

    // Constructor to initialize an empty linked list
    public Link() {
        this.head = null;
    }

    // Function to insert a node at the start of the linked list
    public void insertAtStart(int data) {
        // Create a new node with the given data
        Node newNode = new Node(data);

        // Make the new node point to the current head
        newNode.next = head;

        // Update head to point to the new node
        head = newNode;

        System.out.println("Inserted " + data + " at the start of the list");
    }

    // Function to display the linked list
    public void display() {
        if (head == null) {
            System.out.println("List is empty");
            return;
        }

        System.out.print("Linked List: ");
        Node current = head;
        while (current != null) {
            System.out.print(current.data + " -> ");
            current = current.next;
        }
        System.out.println("null");
    }

    // Function to get the size of the linked list
    public int size() {
        int count = 0;
        Node current = head;
        while (current != null) {
            count++;
            current = current.next;
        }
        return count;
    }

    // Function to check if the list is empty
    public boolean isEmpty() {
        return head == null;
    }

    public static void main(String[] args) {
        // Create a new linked list
        Link list = new Link();

        // Demonstrate inserting at start
        System.out.println("=== Linked List Insert at Start Demo ===");

        // Insert some elements at the start
        list.insertAtStart(10);
        list.display();

        list.insertAtStart(20);
        list.display();

        list.insertAtStart(30);
        list.display();

        list.insertAtStart(40);
        list.display();

        // Show list information
        System.out.println("\nList size: " + list.size());
        System.out.println("Is list empty? " + list.isEmpty());
    }
}
